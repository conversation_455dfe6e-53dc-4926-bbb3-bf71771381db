import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  Response? response;
  bool isLoading = true;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    loadProducts();
  }

  Future<void> loadProducts() async {
    try {
      final result = await getProductsResponse();
      setState(() {
        response = result;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading products: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.blue),
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(35)),
          color: Colors.red,
        ),
        child: Column(
          children: [
            if (isLoading)
              const CircularProgressIndicator()
            else if (errorMessage.isNotEmpty)
              Text(
                errorMessage,
                style: const TextStyle(fontSize: 18, color: Colors.white),
              )
            else if (response != null)
              Text(
                "title products: ${response!.data["product"]["title"]}",
                style: const TextStyle(fontSize: 35, color: Colors.white),
              ),
          ],
        ),
      ),
    );
  }
}

Future<Response> getProductsResponse() async {
  final Dio dio = Dio();
  final Response response = await dio.get("https://dummyjson.com/products");
  // print(response.statusCode.toString());
  return response;
}

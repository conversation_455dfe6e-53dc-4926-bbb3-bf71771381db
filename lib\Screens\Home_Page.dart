import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

// صفحة عرض المنتجات
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // متغيرات لحفظ البيانات
  String productCount = "جاري التحميل..."; // عدد المنتجات
  String firstProductName = ""; // اسم أول منتج
  bool isLoading = true; // هل التطبيق يحمل البيانات؟

  @override
  void initState() {
    super.initState();
    getProductsFromInternet(); // جلب البيانات عند بدء التطبيق
  }

  // دالة لجلب البيانات من الإنترنت
  Future<void> getProductsFromInternet() async {
    try {
      // إنشاء أداة للاتصال بالإنترنت
      Dio dio = Dio();
      // جلب البيانات من الموقع
      Response response = await dio.get("https://dummyjson.com/products");
      // استخراج البيانات المطلوبة
      // int totalProducts = response.data["total"];
      // String firstProduct = response.data["products"][0]["title"];
      // تحديث الشاشة بالبيانات الجديدة
      setState(() {
        productCount = "عدد المنتجات: ${response.data["total"]}";
        firstProductName = "أول منتج: ${response.data["products"][0]["title"]}";
        isLoading = false;
      });
    } catch (error) {
      // في حالة حدوث خطأ
      setState(() {
        productCount = "حدث خطأ في التحميل";
        firstProductName = "";
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط علوي أزرق
      appBar: AppBar(
        backgroundColor: Colors.blue,
        title: Text("تطبيق المنتجات"),
      ),

      // محتوى الصفحة
      body: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20),
        margin: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // إذا كان التطبيق يحمل البيانات
            if (isLoading)
              Column(
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 20),
                  Text(
                    "جاري تحميل البيانات...",
                    style: TextStyle(color: Colors.white, fontSize: 18),
                  ),
                ],
              )
            // إذا انتهى التحميل
            else
              Column(
                children: [
                  // عرض عدد المنتجات
                  Text(
                    productCount,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  SizedBox(height: 20), // مسافة فارغة
                  // عرض اسم أول منتج
                  Text(
                    firstProductName,
                    style: TextStyle(color: Colors.white, fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}

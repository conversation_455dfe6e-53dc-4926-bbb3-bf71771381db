import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

class HomePage extends StatelessWidget {
  late Response response;
  const HomePage({super.key, required this.response});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.blue),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 130, vertical: 400),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(35)),
          color: Colors.red,
        ),
        child: Column(
          children: [
            Text(
              "the title products is${response.data["products"]["title"]}",
              style: TextStyle(fontSize: 35),
            ),
          ],
        ),
      ),
    );
  }
}

Future<Response> getProductsResponse() async {
  final Dio dio = Dio();
  final Response response = await dio.get("https://dummyjson.com/products");
  // print(response.statusCode.toString());
  return response;
}

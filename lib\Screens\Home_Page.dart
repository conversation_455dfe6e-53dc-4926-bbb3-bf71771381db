import 'package:flutter/material.dart';
import 'package:dio/dio.dart';



class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.blue),
      body: Container(
        decoration: BoxDecoration(
        ),
        child: Column(children: [Text("the total is ")]),
      ),
    );
  }
}
Future<Response> getProductsResponse() async {
  final Dio dio = Dio();
  final Response response = await dio.get("https://dummyjson.com/products");
  // print(response.statusCode.toString());
  return response;
}